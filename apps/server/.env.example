# =============================================================================
# SERVER ENVIRONMENT CONFIGURATION
# =============================================================================
# Copy this file to .env in the same directory (apps/server/.env)
# This is for running the server locally while using Docker for infrastructure
# =============================================================================

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Database connection URL (using Docker PostgreSQL)
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/freelance_market_dev

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Environment
NODE_ENV=development

# Server port
PORT=4000

# JWT Secret Key for development
# Generate with: node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
JWT_SECRET=your-super-secret-jwt-key-for-development

# =============================================================================
# KAFKA CONFIGURATION
# =============================================================================
# Kafka broker (using Docker Kafka)
KAFKA_BROKER=localhost:9092
KAFKA_CLIENT_ID=freelance-market-service
KAFKA_GROUP_ID=freelance-market-group

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
# SMTP settings
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false

# Email credentials (Use App Password for Gmail)
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=FreelanceMarket <<EMAIL>>

# =============================================================================
# ADMIN USER CONFIGURATION
# =============================================================================
# Default admin user for seeding
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# =============================================================================
# GRAPHQL CONFIGURATION
# =============================================================================
# Enable GraphQL Playground in development
GRAPHQL_PLAYGROUND=true
GRAPHQL_INTROSPECTION=true
