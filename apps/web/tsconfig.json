{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@components/*": ["./src/components/*"], "@contexts/*": ["./src/contexts/*"], "@lib/*": ["./src/lib/*"], "@validation": ["./src/lib/validation/index.ts"], "@validation/*": ["./src/lib/validation/*"], "@graphql": ["./src/lib/graphql/queries.ts"], "@graphql/*": ["./src/lib/graphql/*"], "@types/*": ["./src/types/*"], "@app/*": ["./src/app/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}