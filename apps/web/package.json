{"name": "web", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit", "codegen": "graphql-codegen --config codegen.ts", "codegen:watch": "graphql-codegen --config codegen.ts --watch"}, "dependencies": {"@apollo/client": "^3.8.8", "@heroicons/react": "^2.0.18", "@repo/shared-types": "workspace:*", "@repo/ui": "workspace:*", "graphql": "^16.8.1", "graphql-ws": "^6.0.5", "next": "^15.3.0", "react": "^19.1.0", "react-dom": "^19.1.0", "zod": "^3.25.30"}, "devDependencies": {"@graphql-codegen/cli": "^5.0.0", "@graphql-codegen/client-preset": "^4.1.0", "@graphql-codegen/introspection": "^4.0.0", "@graphql-codegen/typescript": "^4.0.1", "@graphql-codegen/typescript-operations": "^4.0.1", "@graphql-codegen/typescript-react-apollo": "^4.1.0", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "autoprefixer": "^10.4.21", "eslint": "^9.27.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "5.8.2"}}