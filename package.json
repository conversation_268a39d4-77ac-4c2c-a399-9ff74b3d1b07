{"name": "freelance-marketplace", "version": "1.0.0", "private": true, "description": "A comprehensive freelance marketplace platform", "scripts": {"dev": "turbo dev", "build": "turbo build", "lint": "turbo lint", "check-types": "turbo check-types", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "clean": "turbo clean", "docker:dev": "./scripts/docker-dev.sh", "docker:build": "./scripts/docker-build.sh", "docker:deploy": "./scripts/docker-deploy.sh", "docker:down": "docker-compose down -v", "docker:logs": "docker-compose logs -f"}, "devDependencies": {"prettier": "^3.5.3", "turbo": "^2.5.3"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=20.0.0", "pnpm": ">=9.0.0"}}