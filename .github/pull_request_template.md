# Pull Request

## Description
<!-- Provide a brief description of the changes in this PR -->

## Type of Change
<!-- Mark the relevant option with an "x" -->
- [ ] 🐛 Bug fix (non-breaking change which fixes an issue)
- [ ] ✨ New feature (non-breaking change which adds functionality)
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] 📚 Documentation update
- [ ] 🔧 Refactoring (no functional changes)
- [ ] ⚡ Performance improvement
- [ ] 🧪 Test addition or improvement
- [ ] 🔨 Build/CI changes

## Related Issues
<!-- Link to related issues using "Fixes #123" or "Closes #123" -->
- Fixes #
- Related to #

## Changes Made
<!-- List the main changes made in this PR -->
- 
- 
- 

## Screenshots (if applicable)
<!-- Add screenshots to help explain your changes -->

## Testing
<!-- Describe the tests you ran to verify your changes -->
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] Docker build succeeds
- [ ] All CI checks pass

### Test Instructions
<!-- Provide step-by-step instructions for testing this PR -->
1. 
2. 
3. 

## Checklist
<!-- Mark completed items with an "x" -->
- [ ] My code follows the project's style guidelines
- [ ] I have performed a self-review of my code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] Any dependent changes have been merged and published

## Database Changes
<!-- If this PR includes database changes, describe them -->
- [ ] No database changes
- [ ] Schema changes (migrations included)
- [ ] Data migrations included
- [ ] Seed data changes

## Breaking Changes
<!-- If this PR includes breaking changes, describe them and migration steps -->
- [ ] No breaking changes
- [ ] Breaking changes (describe below)

### Migration Steps
<!-- If there are breaking changes, provide migration steps -->

## Additional Notes
<!-- Add any additional notes, concerns, or context for reviewers -->

## Reviewer Notes
<!-- Any specific areas you'd like reviewers to focus on -->
- 
- 

---
**By submitting this PR, I confirm that my contribution is made under the terms of the project license.**
