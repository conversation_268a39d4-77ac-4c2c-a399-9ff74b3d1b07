# Auto-labeling configuration for PRs based on changed files

# Frontend changes
'frontend':
  - 'apps/web/**/*'
  - '**/*.tsx'
  - '**/*.jsx'
  - '**/*.css'
  - '**/*.scss'
  - '**/*.module.css'

# Backend changes
'backend':
  - 'apps/server/**/*'
  - '!apps/server/**/*.spec.ts'
  - '!apps/server/**/*.test.ts'

# Database changes
'database':
  - 'packages/database/**/*'
  - '**/prisma/**/*'
  - '**/*.sql'
  - '**/migrations/**/*'
  - '**/schema.prisma'
  - '**/seed.ts'

# Docker changes
'docker':
  - '**/Dockerfile*'
  - 'docker-compose*.yml'
  - 'docker-compose*.yaml'
  - '.dockerignore'
  - 'scripts/docker-*.sh'
  - '**/docker/**/*'

# CI/CD changes
'ci/cd':
  - '.github/workflows/**/*'
  - '.github/actions/**/*'

# Documentation changes
'documentation':
  - '**/*.md'
  - 'docs/**/*'
  - '.github/**/*.md'
  - '**/README*'
  - '**/CHANGELOG*'
  - '**/LICENSE*'

# Configuration changes
'config':
  - '**/*.json'
  - '**/*.yml'
  - '**/*.yaml'
  - '**/*.toml'
  - '**/*.config.*'
  - '**/tsconfig*.json'
  - '**/.env*'
  - '!**/package.json'
  - '!**/pnpm-lock.yaml'

# Dependencies
'dependencies':
  - '**/package.json'
  - '**/pnpm-lock.yaml'
  - '**/yarn.lock'
  - '**/package-lock.json'

# Testing
'testing':
  - '**/*.test.*'
  - '**/*.spec.*'
  - '**/test/**/*'
  - '**/tests/**/*'
  - '**/__tests__/**/*'
  - '**/jest.config.*'
  - '**/vitest.config.*'

# Shared packages
'shared':
  - 'packages/**/*'

# Types
'types':
  - 'packages/shared-types/**/*'
  - '**/*.d.ts'
  - '**/types/**/*'

# UI components
'ui':
  - 'packages/ui/**/*'
  - '**/components/**/*'

# Security
'security':
  - '.github/workflows/codeql.yml'
  - '.github/workflows/security.yml'
  - '**/security/**/*'

# Performance
'performance':
  - '**/performance/**/*'
  - '**/*.perf.*'

# Bug fix
'bug':
  - any: ['fix', 'bug', 'hotfix', 'patch']

# Feature
'feature':
  - any: ['feat', 'feature', 'add', 'new']

# Refactor
'refactor':
  - any: ['refactor', 'refactoring', 'restructure']

# Chore
'chore':
  - any: ['chore', 'maintenance', 'update', 'upgrade']

# API changes
'api':
  - '**/api/**/*'
  - '**/graphql/**/*'
  - '**/*.graphql'
  - '**/*.gql'
  - '**/resolvers/**/*'
  - '**/schema/**/*'

# Authentication & Authorization
'auth':
  - '**/auth/**/*'
  - '**/authentication/**/*'
  - '**/authorization/**/*'
  - '**/jwt/**/*'
  - '**/passport/**/*'

# Environment & Deployment
'environment':
  - '**/.env*'
  - '**/environment/**/*'
  - '**/deploy/**/*'
  - '**/deployment/**/*'
