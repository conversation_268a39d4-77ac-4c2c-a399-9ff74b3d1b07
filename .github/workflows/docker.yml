name: Docker Build & Deploy

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME_SERVER: ${{ github.repository }}/server
  IMAGE_NAME_WEB: ${{ github.repository }}/web

jobs:
  # Job 1: Build and Test Docker Images
  docker-build:
    name: Build Docker Images
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata for server
        id: meta-server
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME_SERVER }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix={{branch}}-

      - name: Extract metadata for web
        id: meta-web
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME_WEB }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix={{branch}}-

      - name: Build and push server image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./apps/server/Dockerfile
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta-server.outputs.tags }}
          labels: ${{ steps.meta-server.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

      - name: Build and push web image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./apps/web/Dockerfile
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta-web.outputs.tags }}
          labels: ${{ steps.meta-web.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

  # Job 2: Test Docker Compose Setup
  docker-compose-test:
    name: Test Docker Compose
    runs-on: ubuntu-latest
    needs: [docker-build]
    if: github.event_name == 'pull_request'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Create test environment file
        run: |
          cp .env.example .env
          echo "POSTGRES_PASSWORD=test-password" >> .env
          echo "JWT_SECRET=test-jwt-secret-for-docker-compose" >> .env

      - name: Test docker-compose.dev.yml
        run: |
          docker-compose -f docker-compose.dev.yml up -d
          sleep 30
          
          # Test PostgreSQL
          docker-compose -f docker-compose.dev.yml exec -T postgres pg_isready -U postgres
          
          # Test Kafka
          docker-compose -f docker-compose.dev.yml exec -T kafka kafka-topics --bootstrap-server localhost:9092 --list
          
          # Cleanup
          docker-compose -f docker-compose.dev.yml down -v

      - name: Test full docker-compose.yml
        run: |
          docker-compose up -d --build
          sleep 60
          
          # Test services health
          curl -f http://localhost:4000/health || exit 1
          curl -f http://localhost:3000 || exit 1
          
          # Cleanup
          docker-compose down -v

  # Job 3: Security Scan
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: [docker-build]
    if: github.event_name != 'pull_request'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner on server image
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME_SERVER }}:${{ github.sha }}
          format: 'sarif'
          output: 'trivy-server-results.sarif'

      - name: Run Trivy vulnerability scanner on web image
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME_WEB }}:${{ github.sha }}
          format: 'sarif'
          output: 'trivy-web-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: '.'
