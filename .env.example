# =============================================================================
# FREELANCE MARKETPLACE - DEVELOPMENT ENVIRONMENT CONFIGURATION
# =============================================================================
# Copy this file to .env and update the values according to your environment
#
# For Docker Development: Copy to root .env
# For Local Development: Copy to apps/server/.env
# =============================================================================

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL database settings
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=freelance_market
POSTGRES_PORT=5432

# Database connection URL
# For Local Development: postgresql://postgres:postgres@localhost:5432/freelance_market_dev
# For Docker: ********************************************/freelance_market
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/freelance_market_dev

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Environment (development, production, test)
NODE_ENV=development

# Server port
PORT=4000

# JWT Secret Key (Generate a secure one for development)
# Generate with: node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
JWT_SECRET=your-super-secret-jwt-key-for-development

# =============================================================================
# KAFKA CONFIGURATION (Event Streaming)
# =============================================================================
# Kafka broker address
# For Local Development: localhost:9092
# For Docker: kafka:29092
KAFKA_BROKER=localhost:9092

# Kafka client configuration
KAFKA_CLIENT_ID=freelance-market-service
KAFKA_GROUP_ID=freelance-market-group

# =============================================================================
# EMAIL CONFIGURATION (Nodemailer)
# =============================================================================
# SMTP server settings
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false

# Email credentials (Use App Password for Gmail)
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# From address for outgoing emails
EMAIL_FROM=FreelanceMarket <<EMAIL>>

# =============================================================================
# ADMIN USER CONFIGURATION (For Database Seeding)
# =============================================================================
# Default admin user credentials
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# =============================================================================
# FRONTEND CONFIGURATION (Next.js)
# =============================================================================
# GraphQL API endpoints for development
NEXT_PUBLIC_GRAPHQL_URL=http://localhost:4000/graphql
NEXT_PUBLIC_GRAPHQL_WS_URL=ws://localhost:4000/graphql

# =============================================================================
# GRAPHQL CONFIGURATION
# =============================================================================
# Enable GraphQL Playground for development
GRAPHQL_PLAYGROUND=true

# Enable GraphQL introspection for development
GRAPHQL_INTROSPECTION=true

# =============================================================================
# DEVELOPMENT SPECIFIC
# =============================================================================
# Enable hot reload for development
# HOT_RELOAD=true

# Enable debug mode
# DEBUG=true

# Log level (error, warn, info, debug)
# LOG_LEVEL=debug
