# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/
.next/
out/

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory
coverage/
.nyc_output

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Turbo
.turbo

# TypeScript
*.tsbuildinfo
next-env.d.ts

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# Docker
docker-compose.yml
docker-compose.dev.yml
Dockerfile
.dockerignore

# Documentation
README.md
DOCKER.md
*.md

# Scripts (not needed in container)
scripts/

# Testing
test/
*.spec.ts
*.test.ts

# Temporary folders
tmp/
temp/
