services:
  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: freelance-postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      POSTGRES_DB: ${POSTGRES_DB:-freelance_market}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./packages/database/init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    networks:
      - freelance-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Zookeeper for Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: freelance-zookeeper
    restart: unless-stopped
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    networks:
      - freelance-network
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_logs:/var/lib/zookeeper/log

  # Kafka Message Broker
  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: freelance-kafka
    restart: unless-stopped
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
    ports:
      - "9092:9092"
    networks:
      - freelance-network
    volumes:
      - kafka_data:/var/lib/kafka/data
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "localhost:9092", "--list"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kafka UI for monitoring
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: freelance-kafka-ui
    restart: unless-stopped
    depends_on:
      - kafka
    environment:
      KAFKA_CLUSTERS_0_NAME: freelance-cluster
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181
    ports:
      - "8080:8080"
    networks:
      - freelance-network

  # Backend API Server
  server:
    build:
      context: .
      dockerfile: apps/server/Dockerfile
    container_name: freelance-server
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      NODE_ENV: development
      PORT: 4000
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@postgres:5432/${POSTGRES_DB:-freelance_market}
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-for-development}
      KAFKA_BROKER: kafka:29092
      EMAIL_HOST: ${EMAIL_HOST:-smtp.gmail.com}
      EMAIL_PORT: ${EMAIL_PORT:-587}
      EMAIL_SECURE: ${EMAIL_SECURE:-false}
      EMAIL_USER: ${EMAIL_USER:-}
      EMAIL_PASS: ${EMAIL_PASS:-}
      EMAIL_FROM: ${EMAIL_FROM:-FreelanceMarket <<EMAIL>>}
    ports:
      - "4000:4000"
    networks:
      - freelance-network
    volumes:
      - ./apps/server/uploads:/app/uploads
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:4000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 90s

  # Frontend Web Application
  web:
    build:
      context: .
      dockerfile: apps/web/Dockerfile
    container_name: freelance-web
    restart: unless-stopped
    depends_on:
      - server
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_GRAPHQL_URL: http://localhost:4000/graphql
      NEXT_PUBLIC_GRAPHQL_WS_URL: ws://localhost:4000/graphql
    ports:
      - "3000:3000"
    networks:
      - freelance-network

networks:
  freelance-network:
    driver: bridge

volumes:
  postgres_data:
    driver: local
  zookeeper_data:
    driver: local
  zookeeper_logs:
    driver: local
  kafka_data:
    driver: local
