# Example: How labeler integrates with your CI/CD
name: Auto Label PRs

on:
  pull_request:
    types: [opened, synchronize]

jobs:
  label:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/labeler@v4
        with:
          repo-token: "${{ secrets.GITHUB_TOKEN }}"

  # Conditional jobs based on labels
  frontend-tests:
    needs: label
    if: contains(github.event.pull_request.labels.*.name, 'frontend')
    runs-on: ubuntu-latest
    steps:
      - name: Run frontend tests
        run: |
          cd apps/web
          npm test

  backend-tests:
    needs: label
    if: contains(github.event.pull_request.labels.*.name, 'backend')
    runs-on: ubuntu-latest
    steps:
      - name: Run backend tests
        run: |
          cd apps/server
          npm test

  database-review:
    needs: label
    if: contains(github.event.pull_request.labels.*.name, 'database')
    runs-on: ubuntu-latest
    steps:
      - name: Request DBA review
        run: |
          # Automatically request review from database team
          gh pr review --request-reviewer @database-team

  docker-security:
    needs: label
    if: contains(github.event.pull_request.labels.*.name, 'docker')
    runs-on: ubuntu-latest
    steps:
      - name: Docker security scan
        run: |
          # Run container security scans
          docker scan apps/server/Dockerfile
