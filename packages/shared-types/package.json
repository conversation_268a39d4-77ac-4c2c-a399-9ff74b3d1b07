{"name": "@repo/shared-types", "version": "0.0.0", "private": true, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "lint": "eslint . --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"@repo/database": "workspace:*"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "eslint": "^9.27.0", "tsup": "^8.0.2", "typescript": "5.8.2"}}