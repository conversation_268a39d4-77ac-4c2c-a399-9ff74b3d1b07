{"name": "@repo/database", "version": "0.0.0", "private": true, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./src/index.d.ts", "scripts": {"build": "tsup src/index.ts --format cjs,esm", "db:generate": "dotenv -e ../../.env -- npx prisma generate", "db:push": "dotenv -e ../../.env -- npx prisma db push", "db:migrate": "dotenv -e ../../.env -- npx prisma migrate deploy", "db:studio": "dotenv -e ../../.env -- npx prisma studio", "db:reset": "dotenv -e ../../.env -- npx prisma migrate reset --force"}, "dependencies": {"@prisma/client": "^6.8.2", "dotenv-cli": "^8.0.0", "prisma": "^6.8.2"}, "devDependencies": {"tsup": "^8.0.2", "typescript": "5.8.2"}}